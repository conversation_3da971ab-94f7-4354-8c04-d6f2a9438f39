// Basic JavaScript file
console.log('Page loaded!');

//get data from the 🤟 StreamElements 🤟 data injection
const name = '{{name}}';
const animation = 'wobble';

// vanilla es6 query selection (can use libraries and frameworks too)
const userNameContainer = document.querySelector('#username-container');

// change the inner html to animate it 🤪
userNameContainer.innerHTML = stringToAnimatedHTML(name, animation);

/**
 * return an html, with animation
 * @param s: the text
 * @param anim: the animation to use on the text
 * @returns {string}
 */
function stringToAnimatedHTML(s, anim) {
    let stringAsArray = s.split('');
    stringAsArray = stringAsArray.map((letter) => {
        return `<span class="animated-letter ${anim}">${letter}</span>`
    });
    return stringAsArray.join('');

}

  function removeAlert() {
    const alert = document.querySelector('.alert-success');
    if (alert) {
      alert.classList.remove('slideInFromTop'); // optional if you want to reset entry state
      alert.classList.add('alert-leave');
      setTimeout(() => {
        alert.remove(); // or use alert.style.display = 'none'; if you want to hide without removing
      }, 600); // match the animation duration (0.6s)
    }
  }

  // Auto-remove after 9 seconds
  setTimeout(removeAlert, 9000);