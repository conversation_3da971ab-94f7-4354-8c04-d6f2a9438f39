// Wait for DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('Test page loaded successfully!');
    
    // Initialize all functionality
    initializeColorChanger();
    initializeToggleContent();
    initializeCounter();
    initializeForm();
    initializeNavigation();
    
    // Add welcome message
    showWelcomeMessage();
});

// Background color changer functionality
function initializeColorChanger() {
    const colorBtn = document.getElementById('colorBtn');
    const body = document.body;
    let currentVariant = 0;
    const variants = ['', 'bg-variant-1', 'bg-variant-2', 'bg-variant-3', 'bg-variant-4'];
    
    colorBtn.addEventListener('click', function() {
        // Remove current variant class
        body.className = body.className.replace(/bg-variant-\d+/g, '');
        
        // Cycle to next variant
        currentVariant = (currentVariant + 1) % variants.length;
        
        if (variants[currentVariant]) {
            body.classList.add(variants[currentVariant]);
        }
        
        // Add visual feedback
        colorBtn.style.transform = 'scale(0.95)';
        setTimeout(() => {
            colorBtn.style.transform = '';
        }, 150);
        
        console.log(`Background changed to variant: ${currentVariant}`);
    });
}

// Toggle content functionality
function initializeToggleContent() {
    const toggleBtn = document.getElementById('toggleBtn');
    const toggleContent = document.getElementById('toggleContent');
    let isVisible = true;
    
    toggleBtn.addEventListener('click', function() {
        isVisible = !isVisible;
        
        if (isVisible) {
            toggleContent.classList.remove('hidden');
            toggleBtn.textContent = 'Hide Content';
        } else {
            toggleContent.classList.add('hidden');
            toggleBtn.textContent = 'Show Content';
        }
        
        console.log(`Content visibility: ${isVisible}`);
    });
}

// Counter functionality
function initializeCounter() {
    const decrementBtn = document.getElementById('decrementBtn');
    const incrementBtn = document.getElementById('incrementBtn');
    const counterValue = document.getElementById('counterValue');
    let count = 0;
    
    function updateCounter() {
        counterValue.textContent = count;
        
        // Add animation effect
        counterValue.style.transform = 'scale(1.2)';
        setTimeout(() => {
            counterValue.style.transform = 'scale(1)';
        }, 200);
        
        // Change color based on value
        if (count > 0) {
            counterValue.style.color = '#48bb78';
        } else if (count < 0) {
            counterValue.style.color = '#f56565';
        } else {
            counterValue.style.color = '#667eea';
        }
    }
    
    incrementBtn.addEventListener('click', function() {
        count++;
        updateCounter();
        console.log(`Counter incremented to: ${count}`);
    });
    
    decrementBtn.addEventListener('click', function() {
        count--;
        updateCounter();
        console.log(`Counter decremented to: ${count}`);
    });
}

// Form functionality
function initializeForm() {
    const form = document.getElementById('testForm');
    const formOutput = document.getElementById('formOutput');
    const nameInput = document.getElementById('nameInput');
    const emailInput = document.getElementById('emailInput');
    
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const name = nameInput.value.trim();
        const email = emailInput.value.trim();
        
        if (!name || !email) {
            showFormMessage('Please fill in all fields!', 'error');
            return;
        }
        
        if (!isValidEmail(email)) {
            showFormMessage('Please enter a valid email address!', 'error');
            return;
        }
        
        // Simulate form submission
        showFormMessage('Form submitted successfully!', 'success');
        
        // Log the data
        console.log('Form submitted:', { name, email });
        
        // Clear form after successful submission
        setTimeout(() => {
            form.reset();
            formOutput.style.display = 'none';
        }, 3000);
    });
    
    function showFormMessage(message, type) {
        formOutput.textContent = message;
        formOutput.style.display = 'block';
        
        if (type === 'error') {
            formOutput.style.background = 'rgba(245, 101, 101, 0.1)';
            formOutput.style.borderLeftColor = '#f56565';
        } else {
            formOutput.style.background = 'rgba(72, 187, 120, 0.1)';
            formOutput.style.borderLeftColor = '#48bb78';
        }
    }
    
    function isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
}

// Navigation functionality
function initializeNavigation() {
    const navLinks = document.querySelectorAll('nav a');
    
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetId = this.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);
            
            if (targetElement) {
                targetElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
            
            // Add active state
            navLinks.forEach(l => l.classList.remove('active'));
            this.classList.add('active');
            
            console.log(`Navigated to: ${targetId}`);
        });
    });
}

// Welcome message
function showWelcomeMessage() {
    setTimeout(() => {
        console.log('🎉 Welcome to the test page!');
        console.log('Try the following features:');
        console.log('• Change background colors');
        console.log('• Toggle content visibility');
        console.log('• Use the counter');
        console.log('• Submit the form');
        console.log('• Navigate using the menu');
    }, 1000);
}

// Utility functions
function getRandomColor() {
    const colors = [
        '#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57',
        '#ff9ff3', '#54a0ff', '#5f27cd', '#00d2d3', '#ff9f43'
    ];
    return colors[Math.floor(Math.random() * colors.length)];
}

// Add some interactive console commands for testing
window.testFunctions = {
    changeBackground: function(variant) {
        const body = document.body;
        body.className = body.className.replace(/bg-variant-\d+/g, '');
        if (variant && variant >= 1 && variant <= 4) {
            body.classList.add(`bg-variant-${variant}`);
        }
        console.log(`Background changed to variant: ${variant || 'default'}`);
    },
    
    setCounter: function(value) {
        const counterValue = document.getElementById('counterValue');
        if (counterValue && typeof value === 'number') {
            counterValue.textContent = value;
            console.log(`Counter set to: ${value}`);
        }
    },
    
    logElements: function() {
        console.log('Available elements:');
        console.log('• colorBtn:', document.getElementById('colorBtn'));
        console.log('• toggleBtn:', document.getElementById('toggleBtn'));
        console.log('• counterValue:', document.getElementById('counterValue'));
        console.log('• testForm:', document.getElementById('testForm'));
    }
};

console.log('💡 Tip: Use window.testFunctions for additional testing commands!');
