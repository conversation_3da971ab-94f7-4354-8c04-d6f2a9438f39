<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Page</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <header>
        <h1>Welcome to the Test Page</h1>
        <nav>
            <ul>
                <li><a href="#home">Home</a></li>
                <li><a href="#about">About</a></li>
                <li><a href="#contact">Contact</a></li>
            </ul>
        </nav>
    </header>

    <main>
        <section id="home">
            <h2>Interactive Elements</h2>
            <div class="button-container">
                <button id="colorBtn" class="btn">Change Background Color</button>
                <button id="toggleBtn" class="btn">Toggle Content</button>
            </div>

            <div id="toggleContent" class="content-box">
                <p>This content can be toggled on and off!</p>
                <p>Click the toggle button to see the effect.</p>
            </div>
        </section>

        <section id="counter">
            <h2>Counter Test</h2>
            <div class="counter-container">
                <button id="decrementBtn" class="btn">-</button>
                <span id="counterValue">0</span>
                <button id="incrementBtn" class="btn">+</button>
            </div>
        </section>

        <section id="form-test">
            <h2>Form Test</h2>
            <form id="testForm">
                <div class="form-group">
                    <label for="nameInput">Name:</label>
                    <input type="text" id="nameInput" placeholder="Enter your name">
                </div>
                <div class="form-group">
                    <label for="emailInput">Email:</label>
                    <input type="email" id="emailInput" placeholder="Enter your email">
                </div>
                <button type="submit" class="btn">Submit</button>
            </form>
            <div id="formOutput"></div>
        </section>

        <div class="alert-success">
    <div class="alert-icon"><img src="https://i.ibb.co/chdZ2Hy3/check-circle.png"></div>
    <div class="alert-content">
      <div class="alert-title">New Follower</div>
      <div class="alert-description">
        Thanks for the follow, @<span id="username-container">test</span>!
      </div>
      <div class="alert-description"><u>One Step Closer...</u></div>
    </div>
  </div>
    </main>

    <footer>
        <p>&copy; 2024 Test Page. All rights reserved.</p>
    </footer>

    <script src="script.js"></script>
</body>
</html>