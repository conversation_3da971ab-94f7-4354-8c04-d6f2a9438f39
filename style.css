body {
    margin: 0;
    padding: 0;
    font-family: Arial, sans-serif;
}

#container {
    width: 100%;
    height: 100vh;
}
/* custom styling */
@import url('https://cdn.streamelements.com/scripts/animate.min.css');
@import url('https://fonts.googleapis.com/css?family=Nunito');
* {
    font-family: 'Inter', sans-serif;
}

.alert-success::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  animation: shine 2s ease-in-out;
}
  .alert-success {
    display: flex;
    animation: slideInFromTop 0.6s ease-out forwards;
    align-items: flex-start;
    gap: 12px;
    background-color: #E8F5E9;
    border: 1px solid #43A047;
    box-shadow: 0 8px 32px rgba(67, 160, 71, 0.1);
    border-radius: 8px;
    padding: 16px 20px;
    max-width: 600px;
    font-family: sans-serif;
    color: #1e4620;
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1000;
  }

  .alert-content {
    flex: 1;
  }

  .alert-title {
    font-weight: bold;
    margin-bottom: 4px;
  }

  .alert-description {
    font-size: 16px;
    margin-bottom: 8px;
  }

  .alert-link {
    font-size: 16px;
    color: #000;
    text-decoration: none;
  }

  .alert-link:hover {
    text-decoration: underline;
  }

@keyframes slideInFromTop {
  0% {
    opacity: 0;
    transform: translateX(-50%) translateY(-100px);
  }
  100% {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

.animated-letter {
    animation-duration: 2s;
    animation-iteration-count: infinite;
    display: inline-block;
    animation-fill-mode: both;
    color: #000
}

.animated-letter:nth-child(1) {
    animation-delay: 0s;
}

.animated-letter:nth-child(2) {
    animation-delay: 0.1s;
}

.animated-letter:nth-child(3) {
    animation-delay: 0.2s;
}

.animated-letter:nth-child(4) {
    animation-delay: 0.3s;
}

.animated-letter:nth-child(5) {
    animation-delay: 0.4s;
}

@keyframes slideOutToTop {
  0% {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
  100% {
    opacity: 0;
    transform: translateX(-50%) translateY(-100px);
  }
}

.alert-leave {
  animation: slideOutToTop 0.6s ease-in forwards;
}