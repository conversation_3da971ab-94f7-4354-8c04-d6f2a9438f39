/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    transition: background 0.5s ease;
}

/* Header styles */
header {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    padding: 1rem 0;
    position: sticky;
    top: 0;
    z-index: 100;
}

header h1 {
    text-align: center;
    color: white;
    margin-bottom: 1rem;
    font-size: 2.5rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

nav ul {
    list-style: none;
    display: flex;
    justify-content: center;
    gap: 2rem;
}

nav a {
    color: white;
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.1);
}

nav a:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Main content */
main {
    max-width: 1200px;
    margin: 2rem auto;
    padding: 0 1rem;
}

section {
    background: rgba(255, 255, 255, 0.9);
    margin: 2rem 0;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

h2 {
    color: #4a5568;
    margin-bottom: 1.5rem;
    font-size: 1.8rem;
    text-align: center;
}

/* Button styles */
.btn {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 25px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: bold;
    transition: all 0.3s ease;
    margin: 0.5rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
    background: linear-gradient(45deg, #764ba2, #667eea);
}

.btn:active {
    transform: translateY(-1px);
}

/* Button container */
.button-container {
    text-align: center;
    margin-bottom: 2rem;
}

/* Content box */
.content-box {
    background: rgba(102, 126, 234, 0.1);
    padding: 1.5rem;
    border-radius: 10px;
    border-left: 4px solid #667eea;
    margin: 1rem 0;
    transition: all 0.3s ease;
}

.content-box.hidden {
    opacity: 0;
    transform: translateY(-20px);
    max-height: 0;
    padding: 0 1.5rem;
    overflow: hidden;
}

/* Counter styles */
.counter-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    margin: 2rem 0;
}

#counterValue {
    font-size: 2rem;
    font-weight: bold;
    color: #667eea;
    min-width: 60px;
    text-align: center;
    background: rgba(102, 126, 234, 0.1);
    padding: 0.5rem 1rem;
    border-radius: 10px;
}

/* Form styles */
.form-group {
    margin-bottom: 1.5rem;
}

label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: bold;
    color: #4a5568;
}

input[type="text"],
input[type="email"] {
    width: 100%;
    padding: 12px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

input[type="text"]:focus,
input[type="email"]:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

#formOutput {
    margin-top: 1rem;
    padding: 1rem;
    background: rgba(72, 187, 120, 0.1);
    border-radius: 8px;
    border-left: 4px solid #48bb78;
    display: none;
}

/* Footer */
footer {
    text-align: center;
    padding: 2rem;
    color: white;
    background: rgba(0, 0, 0, 0.2);
    margin-top: 2rem;
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

section {
    animation: fadeIn 0.6s ease-out;
}

/* Responsive design */
@media (max-width: 768px) {
    header h1 {
        font-size: 2rem;
    }
    
    nav ul {
        flex-direction: column;
        gap: 1rem;
    }
    
    main {
        margin: 1rem auto;
        padding: 0 0.5rem;
    }
    
    section {
        padding: 1.5rem;
    }
    
    .counter-container {
        flex-direction: column;
        gap: 0.5rem;
    }
}

/* Background color variations */
.bg-variant-1 {
    background: linear-gradient(135deg, #ff6b6b 0%, #feca57 100%);
}

.bg-variant-2 {
    background: linear-gradient(135deg, #48cae4 0%, #023e8a 100%);
}

.bg-variant-3 {
    background: linear-gradient(135deg, #06ffa5 0%, #3d5a80 100%);
}

.bg-variant-4 {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

@import url('https://cdn.streamelements.com/scripts/animate.min.css');
@import url('https://fonts.googleapis.com/css?family=Nunito');
* {
    font-family: 'Inter', sans-serif;
}

.alert-success::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  animation: shine 2s ease-in-out;
}
  .alert-success {
    display: flex;
    animation: slideInBounce 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55) forwards;
    align-items: flex-start;
    gap: 12px;
    background-color: #E8F5E9;
    border: 1px solid #43A047;
    box-shadow: 0 8px 32px rgba(67, 160, 71, 0.1);
    border-radius: 8px;
    padding: 16px 20px;
    max-width: 600px;
    font-family: sans-serif;
    color: #1e4620;
  }

  .alert-icon {
    color: #1e8e3e;
    font-size: 20px;
    margin-top: 2px;
  }

  .alert-content {
    flex: 1;
  }

  .alert-title {
    font-weight: bold;
    margin-bottom: 4px;
  }

  .alert-description {
    font-size: 16px;
    margin-bottom: 8px;
  }

  .alert-link {
    font-size: 16px;
    color: #000;
    text-decoration: none;
  }

  .alert-link:hover {
    text-decoration: underline;
  }

@keyframes slideInBounce {
  0% {
    opacity: 0;
    transform: translateY(-50px) scale(0.8);
  }
  60% {
    opacity: 1;
    transform: translateY(10px) scale(1.05);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.animated-letter {
    animation-duration: 2s;
    animation-iteration-count: infinite;
    display: inline-block;
    animation-fill-mode: both;
    color: #000
}

.animated-letter:nth-child(1) {
    animation-delay: 0s;
}

.animated-letter:nth-child(2) {
    animation-delay: 0.1s;
}

.animated-letter:nth-child(3) {
    animation-delay: 0.2s;
}

.animated-letter:nth-child(4) {
    animation-delay: 0.3s;
}

.animated-letter:nth-child(5) {
    animation-delay: 0.4s;
}